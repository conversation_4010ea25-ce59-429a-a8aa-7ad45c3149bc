import argparse
import json
import logging
import os
import shutil
import tempfile
from pathlib import Path

import torch
from pytorch_lightning import Trainer
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping, LearningRateMonitor

from .vits.lightning import VitsModel

_LOGGER = logging.getLogger(__name__)


class SafeModelCheckpoint(ModelCheckpoint):
    """Custom checkpoint callback that handles cross-device link issues"""

    def _save_checkpoint(self, trainer, filepath):
        temp_dir = Path(filepath).parent
        temp_dir.mkdir(parents=True, exist_ok=True)

        with tempfile.NamedTemporaryFile(dir=temp_dir, delete=False, suffix='.ckpt') as tmp_file:
            temp_path = tmp_file.name

        try:
            trainer.save_checkpoint(temp_path, self.save_weights_only)
            shutil.move(temp_path, filepath)
            _LOGGER.debug(f"Successfully saved checkpoint to {filepath}")
        except Exception as e:
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            _LOGGER.error(f"Failed to save checkpoint: {e}")
            raise e


def copy_to_drive(local_path, drive_path):
    """Safely copy files to Google Drive"""
    try:
        drive_path.parent.mkdir(parents=True, exist_ok=True)
        shutil.copy2(local_path, drive_path)
        _LOGGER.info(f"Copied {local_path} to {drive_path}")
        return True
    except Exception as e:
        _LOGGER.error(f"Failed to copy to drive: {e}")
        return False


def setup_colab_environment():
    """Setup environment for Colab to avoid cross-device link issues"""
    os.environ['TMPDIR'] = '/content/tmp'
    os.makedirs('/content/tmp', exist_ok=True)

    local_ckpt_dir = Path("/content/local_checkpoints")
    local_ckpt_dir.mkdir(exist_ok=True)

    return local_ckpt_dir


def main():
    logging.basicConfig(level=logging.DEBUG)
    logging.getLogger("fsspec").setLevel(logging.WARNING)
    logging.getLogger("matplotlib").setLevel(logging.WARNING)
    logging.getLogger("PIL").setLevel(logging.WARNING)

    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset-dir", type=str, required=True)
    parser.add_argument("--checkpoint-epochs", type=int)
    parser.add_argument("--quality", default="medium", choices=("x-low", "medium", "high"))
    parser.add_argument("--resume_from_single_speaker_checkpoint")
    VitsModel.add_model_specific_args(parser)
    parser.add_argument("--accelerator", type=str)
    parser.add_argument("--devices", type=int)
    parser.add_argument("--log_every_n_steps", type=int)
    parser.add_argument("--max_epochs", type=int)
    parser.add_argument("--seed", type=int, default=1234)
    parser.add_argument("--random_seed", type=bool, default=False)
    parser.add_argument("--resume_from_checkpoint", type=str)
    parser.add_argument("--precision", type=str)
    parser.add_argument("--num_ckpt", type=int, default=1)
    parser.add_argument("--default_root_dir", type=str)
    parser.add_argument("--save_last", type=bool, default=None)
    parser.add_argument("--monitor", type=str, default="val_loss")
    parser.add_argument("--monitor_mode", type=str, default="min")
    parser.add_argument("--early_stop_patience", type=int, default=0)
    parser.add_argument("--use_colab_fix", type=bool, default=True)
    parser.add_argument("--drive_backup_dir", type=str)

    args = parser.parse_args()
    _LOGGER.debug(args)

    args.dataset_dir = Path(args.dataset_dir)

    local_ckpt_dir = None
    if args.use_colab_fix and '/content' in str(args.dataset_dir):
        _LOGGER.info("Detected Colab environment, applying fixes...")
        local_ckpt_dir = setup_colab_environment()

        if not args.default_root_dir:
            args.default_root_dir = str(local_ckpt_dir)

        if not args.drive_backup_dir:
            args.drive_backup_dir = "/content/drive/MyDrive/colab/piper/checkpoints"
    else:
        if not args.default_root_dir:
            args.default_root_dir = str(args.dataset_dir)

    torch.backends.cudnn.benchmark = True

    if args.random_seed:
        seed = torch.seed()
        _LOGGER.debug("Using random seed: %s", seed)
    else:
        torch.manual_seed(args.seed)
        _LOGGER.debug("Using manual seed: %s", args.seed)

    def supports_tensor_cores():
        return torch.cuda.is_available() and torch.cuda.get_device_capability(0)[0] >= 7

    if supports_tensor_cores():
        torch.set_float32_matmul_precision('high')

    config_path = args.dataset_dir / "config.json"
    dataset_path = args.dataset_dir / "dataset.jsonl"

    with open(config_path, "r", encoding="utf-8") as config_file:
        config = json.load(config_file)
        num_symbols = int(config["num_symbols"])
        num_speakers = int(config["num_speakers"])
        sample_rate = int(config["audio"]["sample_rate"])

    allowed_args = [
        "accelerator",
        "devices",
        "log_every_n_steps",
        "max_epochs",
        "precision",
        "default_root_dir",
    ]
    filtered_args = {key: value for key, value in vars(args).items() if key in allowed_args}

    callbacks = []

    if args.checkpoint_epochs is not None:
        if args.use_colab_fix and '/content' in str(args.dataset_dir):
            checkpoint_callback = SafeModelCheckpoint(
                every_n_epochs=args.checkpoint_epochs,
                save_top_k=args.num_ckpt,
                monitor=args.monitor,
                mode=args.monitor_mode,
                save_last=args.save_last
            )
        else:
            checkpoint_callback = ModelCheckpoint(
                every_n_epochs=args.checkpoint_epochs,
                save_top_k=args.num_ckpt,
                monitor=args.monitor,
                mode=args.monitor_mode,
                save_last=args.save_last
            )
        callbacks.append(checkpoint_callback)

    if args.early_stop_patience > 0:
        early_stopping_callback = EarlyStopping(
            monitor='val_loss',
            patience=args.early_stop_patience,
            verbose=True,
            mode='min'
        )
        callbacks.append(early_stopping_callback)

    callbacks.append(LearningRateMonitor(logging_interval='epoch'))

    trainer = Trainer(**filtered_args, callbacks=callbacks)

    dict_args = vars(args)
    if args.quality == "x-low":
        dict_args["hidden_channels"] = 96
        dict_args["inter_channels"] = 96
        dict_args["filter_channels"] = 384
    elif args.quality == "high":
        dict_args["resblock"] = "1"
        dict_args["resblock_kernel_sizes"] = (3, 7, 11)
        dict_args["resblock_dilation_sizes"] = ((1, 3, 5), (1, 3, 5), (1, 3, 5))
        dict_args["upsample_rates"] = (8, 8, 2, 2)
        dict_args["upsample_initial_channel"] = 512
        dict_args["upsample_kernel_sizes"] = (16, 16, 4, 4)

    model = VitsModel(
        num_symbols=num_symbols,
        num_speakers=num_speakers,
        sample_rate=sample_rate,
        dataset=[dataset_path],
        **dict_args,
    )

    if args.resume_from_single_speaker_checkpoint:
        assert num_speakers > 1, "--resume_from_single_speaker_checkpoint is only for multi-speaker models."
        _LOGGER.debug("Resuming from single-speaker checkpoint: %s", args.resume_from_single_speaker_checkpoint)

        model_single = VitsModel.load_from_checkpoint(
            args.resume_from_single_speaker_checkpoint,
            dataset=None,
        )
        g_dict = model_single.model_g.state_dict()
        for key in list(g_dict.keys()):
            if (
                key.startswith("dec.cond")
                or key.startswith("dp.cond")
                or "enc.cond_layer" in key
            ):
                g_dict.pop(key, None)

        load_state_dict(model.model_g, g_dict)
        load_state_dict(model.model_d, model_single.model_d.state_dict())
        _LOGGER.info("Successfully converted single-speaker checkpoint to multi-speaker")

    if args.resume_from_checkpoint:
        _LOGGER.info(f"🔁 Manually loading filtered checkpoint from {args.resume_from_checkpoint}")
        checkpoint = torch.load(args.resume_from_checkpoint, map_location="cpu", weights_only=False)
        pretrained_state = checkpoint["state_dict"]

        exclude_keys = ["cond", "emb_g"]
        filtered_state = {
            k: v for k, v in pretrained_state.items()
            if not any(excl in k for excl in exclude_keys)
        }

        missing_keys, unexpected_keys = model.load_state_dict(filtered_state, strict=False)
        _LOGGER.info(f"✅ Loaded weights with {len(missing_keys)} missing and {len(unexpected_keys)} unexpected keys")

    try:
        trainer.fit(model)
        _LOGGER.info("Training completed successfully!")
    except Exception as e:
        _LOGGER.error(f"Training failed: {e}")
        raise

    if args.use_colab_fix and args.drive_backup_dir and local_ckpt_dir:
        _LOGGER.info("Backing up checkpoints to Google Drive...")
        try:
            local_lightning_logs = local_ckpt_dir / "lightning_logs"
            drive_backup_path = Path(args.drive_backup_dir)

            if local_lightning_logs.exists():
                checkpoint_files = list(local_lightning_logs.rglob("*.ckpt"))

                if checkpoint_files:
                    _LOGGER.info(f"Found {len(checkpoint_files)} checkpoint files to backup")

                    for ckpt_file in checkpoint_files:
                        relative_path = ckpt_file.relative_to(local_lightning_logs)
                        backup_file = drive_backup_path / relative_path

                        success = copy_to_drive(ckpt_file, backup_file)
                        if not success:
                            _LOGGER.warning(f"Failed to backup {ckpt_file}")

                    _LOGGER.info("Checkpoint backup completed!")
                else:
                    _LOGGER.warning("No checkpoint files found to backup")
            else:
                _LOGGER.warning("No lightning_logs directory found")

        except Exception as e:
            _LOGGER.error(f"Error during checkpoint backup: {e}")


def load_state_dict(model, saved_state_dict):
    state_dict = model.state_dict()
    new_state_dict = {}
    for k, v in state_dict.items():
        if k in saved_state_dict:
            new_state_dict[k] = saved_state_dict[k]
        else:
            _LOGGER.debug("%s is not in the checkpoint", k)
            new_state_dict[k] = v
    model.load_state_dict(new_state_dict)


if __name__ == "__main__":
    main()
